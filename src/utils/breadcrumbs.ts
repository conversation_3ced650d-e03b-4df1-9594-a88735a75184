interface PageInfo {
  title: string;
  path: string;
}

// Static mapping of paths to titles
// This approach works better for static sites and avoids dynamic imports
const PAGE_TITLES: Record<string, string> = {
  '/projects': 'Projects',
  '/privacy-policy': 'Privacy Policy',
  '/terms-of-service': 'Terms of Service',
  '/projects/amp-for-bandcamp': 'Amp for Bandcamp',
  '/projects/amp-for-bandcamp/privacy-policy': 'Privacy Policy - Amp for Bandcamp',
};

export function getBreadcrumbs(pathname: string): PageInfo[] {
  const breadcrumbs: PageInfo[] = [];

  // Split the pathname into segments
  const segments = pathname.split('/').filter(Boolean);

  // Build breadcrumbs for each parent path
  for (let i = 0; i < segments.length - 1; i++) {
    const path = '/' + segments.slice(0, i + 1).join('/');
    const title = PAGE_TITLES[path];

    if (title) {
      breadcrumbs.push({
        title,
        path
      });
    }
  }

  return breadcrumbs;
}
