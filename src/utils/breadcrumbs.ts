interface PageInfo {
  title: string;
  path: string;
}

// Cache for loaded page titles to avoid re-importing
const titleCache = new Map<string, string | null>();

async function getPageTitle(path: string): Promise<string | null> {
  // Check cache first
  if (titleCache.has(path)) {
    return titleCache.get(path) || null;
  }

  try {
    // Dynamically import the page module
    const pageModule = await import(`../app${path}/page`);

    let title: string | null = null;

    // Try to extract title from metadata first
    if (pageModule.metadata?.title) {
      title = pageModule.metadata.title.replace(/ — enden\.com$/, '');
    }
    // Fallback to pageTitle constant if available
    else if (pageModule.pageTitle) {
      title = pageModule.pageTitle;
    }

    // Cache the result (even if null)
    titleCache.set(path, title);
    return title;
  } catch (error) {
    // If we can't load the module, cache null and return null
    titleCache.set(path, null);
    return null;
  }
}

export async function getBreadcrumbs(pathname: string): Promise<PageInfo[]> {
  const breadcrumbs: PageInfo[] = [];

  // Split the pathname into segments
  const segments = pathname.split('/').filter(Boolean);

  // Build breadcrumbs for each parent path
  for (let i = 0; i < segments.length - 1; i++) {
    const path = '/' + segments.slice(0, i + 1).join('/');
    const title = await getPageTitle(path);

    if (title) {
      breadcrumbs.push({
        title,
        path
      });
    }
  }

  return breadcrumbs;
}
