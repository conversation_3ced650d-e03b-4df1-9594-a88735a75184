'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import { getBreadcrumbs } from '@/utils/breadcrumbs';

interface PageInfo {
  title: string;
  path: string;
}

export default function SiteTitle() {
  const pathname = usePathname();
  const [breadcrumbs, setBreadcrumbs] = useState<PageInfo[]>([]);
  const [loading, setLoading] = useState(true);

  // Don't show on homepage
  if (pathname === '/') {
    return null;
  }

  useEffect(() => {
    async function loadBreadcrumbs() {
      setLoading(true);
      try {
        const crumbs = await getBreadcrumbs(pathname);
        setBreadcrumbs(crumbs);
      } catch (error) {
        console.error('Failed to load breadcrumbs:', error);
        setBreadcrumbs([]);
      } finally {
        setLoading(false);
      }
    }

    loadBreadcrumbs();
  }, [pathname]);

  return (
    <div className="site-title-wrapper">
      <div className="site-title-content">
        <div className="site-title">
          <Link href="/" aria-label="Back to home">← enden.com</Link>
          {!loading && breadcrumbs.length > 0 && (
            <>
              {breadcrumbs.map((crumb) => (
                <span key={crumb.path}>
                  <span className="breadcrumb-separator"> / </span>
                  <Link href={crumb.path}>{crumb.title}</Link>
                </span>
              ))}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
