'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { getBreadcrumbs } from '@/utils/breadcrumbs';

interface PageInfo {
  title: string;
  path: string;
}

export default function SiteTitle() {
  const pathname = usePathname();

  // Don't show on homepage
  if (pathname === '/') {
    return null;
  }

  const breadcrumbs = getBreadcrumbs(pathname);

  return (
    <div className="site-title-wrapper">
      <div className="site-title-content">
        <div className="site-title">
          <Link href="/" aria-label="Back to home">← enden.com</Link>
          {breadcrumbs.length > 0 && (
            <>
              {breadcrumbs.map((crumb) => (
                <span key={crumb.path}>
                  <span className="breadcrumb-separator"> / </span>
                  <Link href={crumb.path}>{crumb.title}</Link>
                </span>
              ))}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
