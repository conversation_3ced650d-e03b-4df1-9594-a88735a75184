:root {
  /* Colors */
  --background-color: #fff;
  --primary-color: #1802ff;
  --text-color: #333;
  --text-color-light: #666;
  --text-color-dark: #000;
  --border-color: rgba(0, 0, 0, 0.1);
  --kbd-border: #ddd;

  /* Typography */
  --line-height: 1.5;
  --system-ui: system-ui, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
  --font-family: var(--system-ui);

  /* Font sizes */
  --font-size-xs: 0.8rem;
  --font-size-sm: 0.9rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.25rem;
  --font-size-xl: 1.5rem;
  --font-size-2xl: 2rem;
  --font-size-3xl: 2.5rem;

  /* Spacing */
  --spacing-xs: 0.5rem;
  --spacing-sm: 1rem;
  --spacing-md: 1.5rem;
  --spacing-lg: 2rem;
  --spacing-xl: 3rem;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background-color: #121522;
    --primary-color: #456bba;
    --text-color: #abb2c1;
    --text-color-light: #ccced5;
    --text-color-dark: #ffffff;
    --border-color: rgba(0, 0, 0, 0.1);
    --kbd-border: #ddd;
  }
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family);
  line-height: var(--line-height);
  color: var(--text-color);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--background-color);
}

img {
  max-width: 100%;
  height: auto;
}

/* Typography */

h1 {
  font-size: clamp(var(--font-size-2xl), 5vw, var(--font-size-3xl));
  font-weight: 600;
  color: var(--text-color-dark);
}

h2 {
  font-size: clamp(var(--font-size-lg), 4vw, var(--font-size-xl));
  font-weight: normal;
  color: var(--text-color-light);
}

kbd {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  border: 1px solid var(--kbd-border);
  box-shadow: 0 1px 1px rgba(0,0,0,0.1);
  font-family: var(--system-ui), sans-serif;
  font-size: var(--font-size-xs);
  min-width: 40px;
  text-align: center;
  line-height: 1;
  margin-right: var(--spacing-xs);
  vertical-align: text-bottom;
}

b, strong {
  font-weight: 600;
}

/* Links */

a {
  display: inline-flex;
  align-items: center;
  color: var(--primary-color);
  text-decoration: none;
  position: relative;
}

a::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 1px;
  bottom: -1px;
  left: 0;
  background-color: currentColor;
  transform: scaleX(0);
  transition: transform 0.6s cubic-bezier(0.19, 1, 0.22, 1);
}

a:hover::after {
  transform: scaleX(1);
}

.back-link {
  display: inline-block;
  margin-bottom: var(--spacing-sm);
  margin-top: var(--spacing-lg);
  font-size: var(--font-size-sm);
}

.home-links, .links, .footer-links {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: var(--spacing-sm) var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.home-links, .footer-links {
  margin-top: var(--spacing-xl);
  justify-content: center;
}

.footer-links a {
  font-size: var(--font-size-sm);
}

@media (max-width: 768px) {
  .links {
    align-items: flex-start;
  }
  .home-links, .links {
    flex-direction: column;
  }
}

.badges a {
  margin-right: var(--spacing-sm);
  border: 0;
}

/* Layout */

.site-title-wrapper {
  padding: var(--spacing-lg);
  padding-bottom: 0;
}

.site-title-content {
  max-width: 800px;
  margin: 0 auto;
}

.site-title {
  font-size: var(--font-size-xl);
}

.home-page {
  flex: 1;
}

.header {
  text-align: center;
  padding: var(--spacing-xl) var(--spacing-sm);
}

.footer {
  margin-top: auto;
  padding: var(--spacing-md) var(--spacing-sm);
  text-align: center;
}

.footer-info {
  font-size: var(--font-size-sm);
}

.narrow-page {
  flex: 1;
  padding: var(--spacing-lg);
  padding-bottom: var(--spacing-xl);
}

.narrow-content {
  max-width: 800px;
  margin: 0 auto;
}

.narrow-content p {
  margin-bottom: var(--spacing-sm);
  line-height: 1.6;
}

.narrow-content .links a {
  margin-top: 0;
}

.spacer {
  color: var(--primary-color);
  font-size: var(--font-size-xl);
}

.color-blue {
  color: var(--primary-color);
}
